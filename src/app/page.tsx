"use client";

import {
  PricingSection,
  FAQSection,
  BoxingGallery,
  AboutIvan,
} from "@/components";

export default function Home() {
  return (
    <div className="min-h-screen font-sans antialiased">
      {/* Header */}
      <div className="relative flex mx-auto flex-col">
        {/* Logo */}
        <div className="top-12 absolute left-12 md:left-24 z-[9999]">
          <div>
            <h2 className="text-4xl font-bold text-black">
              <span>Ivan</span>
            </h2>
          </div>
        </div>

        {/* CTA Button */}
        <div className="top-12 absolute right-12 md:right-24">
          <div className="h-12">
            <a
              className="inline-block group focus:outline-none"
              href="#contact"
            >
              <div className="relative flex">
                <div className="z-10 flex">
                  <div className="h-full min-h-[40px] max-h-[40px] flex">
                    <div className="bg-red-600 text-white h-full flex items-center px-6 rounded-l-full">
                      <span className="text-lg font-semibold">Train Now</span>
                    </div>
                    <div className="bg-red-600 text-white h-full min-h-[40px] max-h-[40px] flex items-center px-2 rounded-r-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="group-hover:rotate-45 transition-all duration-100 h-4 w-4"
                      >
                        <path d="M7 7h10v10"></path>
                        <path d="M7 17 17 7"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1">
          <div className="relative z-[9999] w-screen">
            <div className="flex items-center justify-center">
              <div className="fixed bottom-10 md:top-10 z-[9999]">
                <div className="max-w-4xl">
                  <div className="flex space-x-4 sticky top-0 z-50 bg-black/60 px-1 py-[3px] rounded-full border border-black">
                    <ul className="flex w-full justify-between">
                      <button
                        onClick={() =>
                          document
                            .getElementById("hero")
                            ?.scrollIntoView({ behavior: "smooth" })
                        }
                        className="relative flex items-center justify-center px-4 py-2 text-lg cursor-pointer font-medium outline-none transition focus-visible:outline-2 hover:bg-white/10 rounded-full"
                      >
                        <div className="z-20 flex items-center">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 text-neutral-100/60"
                          >
                            <path
                              d="M8 17H16M11.0177 2.764L4.23539 8.03912C3.78202 8.39175 3.55534 8.56806 3.39203 8.78886C3.24737 8.98444 3.1396 9.20478 3.07403 9.43905C3 9.70352 3 9.9907 3 10.5651V17.8C3 18.9201 3 19.4801 3.21799 19.908C3.40973 20.2843 3.71569 20.5903 4.09202 20.782C4.51984 21 5.07989 21 6.2 21H17.8C18.9201 21 19.4802 21 19.908 20.782C20.2843 20.5903 20.5903 20.2843 20.782 19.908C21 19.4801 21 18.9201 21 17.8V10.5651C21 9.9907 21 9.70352 20.926 9.43905C20.8604 9.20478 20.7526 8.98444 20.608 8.78886C20.4447 8.56806 20.218 8.39175 19.7646 8.03913L12.9823 2.764C12.631 2.49075 12.4553 2.35412 12.2613 2.3016C12.0902 2.25526 11.9098 2.25526 11.7387 2.3016C11.5447 2.35412 11.369 2.49075 11.0177 2.764Z"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                            />
                          </svg>
                        </div>
                      </button>
                      <button
                        onClick={() =>
                          document
                            .getElementById("gallery")
                            ?.scrollIntoView({ behavior: "smooth" })
                        }
                        className="relative flex items-center justify-center px-4 py-2 text-lg cursor-pointer font-medium outline-none transition focus-visible:outline-2 hover:bg-white/10 rounded-full"
                      >
                        <div className="z-20 flex items-center">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 text-neutral-100/60"
                          >
                            <path
                              d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      </button>
                      <button
                        onClick={() =>
                          document
                            .getElementById("about")
                            ?.scrollIntoView({ behavior: "smooth" })
                        }
                        className="relative flex items-center justify-center px-4 py-2 text-lg cursor-pointer font-medium outline-none transition focus-visible:outline-2 hover:bg-white/10 rounded-full"
                      >
                        <div className="absolute inset-0 bg-red-600 mix-blend-difference rounded-full"></div>
                        <div className="z-20 flex items-center">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 text-red-300"
                          >
                            <circle
                              cx="12"
                              cy="12"
                              r="3"
                              stroke="currentColor"
                              strokeWidth="2"
                            />
                            <path
                              d="M12 1v6m0 6v6m11-7h-6m-6 0H1"
                              stroke="currentColor"
                              strokeWidth="2"
                            />
                          </svg>
                        </div>
                      </button>
                      <button
                        onClick={() =>
                          document
                            .getElementById("price")
                            ?.scrollIntoView({ behavior: "smooth" })
                        }
                        className="relative flex items-center justify-center px-4 py-2 text-lg cursor-pointer font-medium outline-none transition focus-visible:outline-2 hover:bg-white/10 rounded-full"
                      >
                        <div className="z-20 flex items-center">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 text-neutral-100/60"
                          >
                            <path
                              d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      </button>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Hero Section */}
          <main className="overflow-hidden">
            <section id="hero">
              <div className="h-full md:h-[900px]">
                <div className="">
                  <div className="h-full w-full relative md:z-0">
                    {/* Hero Background Image */}
                    <div className="hidden md:block absolute md:top-24 right-0">
                      <div style={{ opacity: 1, transform: "none" }}>
                        <div className="w-[600px] h-[600px] bg-gradient-to-br from-red-500 to-red-700 rounded-full opacity-20 animate-pulse"></div>
                      </div>
                    </div>

                    {/* Main Hero Text */}
                    <div className="absolute top-[calc(100vh-260px)] md:top-56 z-[9999] md:left-16 w-full md:w-auto pr-4">
                      <div style={{ opacity: 1, transform: "none" }}>
                        <span className="flex items-end justify-end md:justify-start flex-col text-right md:text-left tracking-tight pb-3 bg-clip-text font-sans text-transparent bg-gradient-to-t from-neutral-800 to-neutral-950 dark:from-stone-200 dark:to-neutral-200 text-5xl sm:text-6xl lg:text-[6rem] font-bold">
                          <h1 className="md:hidden font-black text-5xl">
                            Train <br />
                            with <br />
                            <span className="font-bold text-6xl text-red-600">
                              Ivan
                            </span>
                          </h1>
                          <h1 className="hidden md:flex items-center font-semibold flex-col text-[12.5rem] pb-4">
                            Train <br />
                            with <br />
                            <span className="font-bold pt-9 sr-only">Ivan</span>
                          </h1>
                        </span>
                      </div>
                    </div>

                    {/* Coach Name Display */}
                    <div className="hidden md:block absolute top-[calc(100vh-530px)] right-7">
                      <h2 className="font-bold pt-9 md:text-[12.5rem] text-red-600">
                        <span
                          className="inline-block mr-[0.25em] whitespace-nowrap"
                          aria-hidden="true"
                        >
                          <span
                            aria-hidden="true"
                            className="inline-block -mr-[0.01em]"
                            style={{
                              opacity: 1,
                              transform: "translateY(0em) translateZ(0px)",
                            }}
                          >
                            I
                          </span>
                          <span
                            aria-hidden="true"
                            className="inline-block -mr-[0.01em]"
                            style={{
                              opacity: 1,
                              transform: "translateY(0em) translateZ(0px)",
                            }}
                          >
                            v
                          </span>
                          <span
                            aria-hidden="true"
                            className="inline-block -mr-[0.01em]"
                            style={{
                              opacity: 1,
                              transform: "translateY(0em) translateZ(0px)",
                            }}
                          >
                            a
                          </span>
                          <span
                            aria-hidden="true"
                            className="inline-block -mr-[0.01em]"
                            style={{
                              opacity: 1,
                              transform: "translateY(0em) translateZ(0px)",
                            }}
                          >
                            n
                          </span>
                        </span>
                      </h2>
                    </div>
                  </div>

                  {/* Mobile Hero Image */}
                  <div className="md:hidden">
                    <div
                      style={{
                        opacity: 0,
                        transform: "translateY(24px) translateZ(0)",
                      }}
                    >
                      <div className="relative h-screen max-h-[1000px] w-full min-h-[500px] lg:min-h-[600px] before:absolute before:inset-0 before:bg-red-600 before:opacity-30 overflow-hidden">
                        <div className="absolute inset-0 h-full w-full bg-gradient-to-br from-red-500 to-red-700 rounded-br-[88px]"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Boxing Gallery */}
            <BoxingGallery />

            {/* About Ivan Section */}
            <AboutIvan />

            {/* Pricing Section */}
            <PricingSection />

            {/* FAQ Section */}
            <FAQSection />
          </main>
        </div>
      </div>
    </div>
  );
}
